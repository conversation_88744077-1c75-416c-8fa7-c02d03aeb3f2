{"id": "NjRhNDIxZDEyNjMxZjYuODcyNTg4NzguYmFjay40Ng==", "property_id": 1, "datetime_created": *************, "datetime_updated": *************, "datetime_post": *************, "datetime_transaction": *************, "user_id": 146053, "user_name": "<PERSON><PERSON>", "room_id": "0", "room_name": "N/A", "reservation_name": "test test", "reservation_identifier": "*************", "res_room_identifier": "*************", "term_code": "", "description": "Cake", "notes": "", "qty": 1, "debit": 0, "debit_money": 0, "debit_scale": 9, "debit_raw": 0, "credit": 5, "credit_money": 500, "credit_scale": 2, "credit_raw": 500, "currency": "USD", "posted": 1, "void": 0, "type": "product", "deleted": 0, "reservation_room_id": null, "parent_id": null, "reservation_id": 130200, "link_id": "1", "source_type": null, "source_id": null, "description_filter": "product_1", "void_id": null, "adjust_root_id": null, "house_account_id": null, "group_profile_id": null, "allotment_block_id": null, "is_private_ha": null, "drawer_id": null, "drawer_action_id": null, "customer_id": 184073, "is_refund": 0, "state": "completed", "readonly": 0, "__op": "c", "__table": "a_htl_financial_transactions", "__source_ts_ms": *************, "__deleted": "false"}