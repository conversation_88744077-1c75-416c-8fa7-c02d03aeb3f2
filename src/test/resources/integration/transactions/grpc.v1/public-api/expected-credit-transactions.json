[{"transaction_id": "57744356724878", "property_id": "1", "transaction_category": "refund", "property_name": "Property Name", "amount": 4000, "currency": "USD", "guest_id": 3063443460225, "reservation_id": "57870451658897", "sub_reservation_id": "roomIdentifier", "transaction_date_time": "2025-01-10T11:24:23.744452", "transaction_date_time_utc": "2025-01-10T11:24:23.744452", "transaction_modified_date_time": "2025-01-10T11:24:23.744452", "transaction_modified_date_time_utc": "2025-01-10T11:24:23.744452", "guest_check_in": "2023-12-10", "guest_check_out": "2023-12-14", "description": "Cash", "quantity": 1, "is_deleted": false, "customer_id": "3063443460225", "source_id": "57870451658897", "source_kind": "RESERVATION", "origin_id": "cash", "external_relation_id": "57872064577722", "external_relation_kind": "PAYMENT", "user_id": "0", "source_datetime": "2025-01-10T11:24:23", "mfd_transaction_id": "50044356724878", "created_at": "2025-01-10T11:24:23.744402", "service_date": "2025-01-10", "transaction_type": "credit", "internal_transaction_code": "9100A", "is_voided": false, "reservation_room_id": 1234}, {"transaction_id": "57744356724876", "property_id": "1", "transaction_category": "payment", "property_name": "Property Name", "amount": -10000, "currency": "USD", "guest_id": 3063443460225, "reservation_id": "57870451658897", "sub_reservation_id": "roomIdentifier", "reservation_room_id": 1234, "transaction_date_time": "2025-01-10T11:23:50.711687", "transaction_date_time_utc": "2025-01-10T11:23:50.711687", "transaction_modified_date_time": "2025-01-10T11:23:50.711687", "transaction_modified_date_time_utc": "2025-01-10T11:23:50.711687", "guest_check_in": "2023-12-10", "guest_check_out": "2023-12-14", "description": "Money@Money!", "quantity": "1", "customer_id": "3063443460225", "source_id": "57870451658897", "source_kind": "RESERVATION", "origin_id": "money@money!", "external_relation_id": "57872064577717", "external_relation_kind": "PAYMENT", "user_id": "0", "is_deleted": false, "source_datetime": "2025-01-10T11:23:49", "created_at": "2025-01-10T11:23:50.711647", "service_date": "2025-01-10", "transaction_type": "credit", "card_type": "VISA", "internal_transaction_code": "9300", "is_voided": false, "mfd_transaction_id": 50044356724876}, {"transaction_id": "57744356720816", "property_id": "1", "transaction_category": "payment", "property_name": "Property Name", "amount": -10000, "currency": "USD", "guest_id": 3063443460225, "reservation_id": "57870451658897", "sub_reservation_id": "roomIdentifier", "reservation_room_id": 1234, "transaction_date_time": "2025-01-10T11:20:55.527648", "transaction_date_time_utc": "2025-01-10T11:20:55.527648", "transaction_modified_date_time": "2025-01-10T11:20:55.527648", "transaction_modified_date_time_utc": "2025-01-10T11:20:55.527648", "guest_check_in": "2023-12-10", "guest_check_out": "2023-12-14", "description": "Money@Money!", "quantity": "1", "customer_id": "3063443460225", "source_id": "57870451658897", "source_kind": "RESERVATION", "origin_id": "money@money!", "external_relation_id": "57872064577686", "external_relation_kind": "PAYMENT", "user_id": "1", "is_deleted": false, "source_datetime": "2025-01-10T11:20:55", "created_at": "2025-01-10T11:20:55.527607", "service_date": "2025-01-10", "transaction_type": "credit", "user_name": "firstName lastName", "internal_transaction_code": "9000", "is_voided": false, "mfd_transaction_id": 50044356720816}]