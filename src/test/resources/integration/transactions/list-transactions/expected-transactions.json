[{"id": "1", "propertyId": "1", "internalTransactionCode": "3000", "amount": 100.22, "currency": "USD", "customerId": "184074", "rootId": "1", "parentId": null, "sourceId": "2", "subSourceId": "2", "sourceKind": "RESERVATION", "externalRelationId": "1", "externalRelationKind": "ADDON", "originId": "211", "routedFrom": null, "quantity": 1, "description": "Breakfast (package inclusive)", "userId": "0", "sourceDatetime": "2023-07-04T14:01:17Z", "transactionDatetime": "2023-07-04T14:01:17Z", "transactionDatetimePropertyTime": "2023-07-04T14:01:17Z", "createdAt": "2023-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": null}, {"id": "2", "propertyId": "1", "internalTransactionCode": "8000", "amount": 10.02, "currency": "USD", "customerId": "184073", "rootId": "3", "parentId": "1", "sourceId": "1", "subSourceId": "18", "sourceKind": "RESERVATION", "externalRelationId": "1", "externalRelationKind": "ADDON", "originId": "123", "routedFrom": null, "quantity": 1, "description": "Tax 10%", "userId": "0", "sourceDatetime": "2023-07-04T14:01:17Z", "transactionDatetime": "2023-07-04T14:01:17Z", "transactionDatetimePropertyTime": "2023-07-04T14:01:17Z", "createdAt": "2023-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************-1", "subSourceIdentifier": null}, {"id": "3", "propertyId": "1", "internalTransactionCode": "8100", "amount": 10.02, "currency": "USD", "customerId": "184074", "rootId": "2", "parentId": "1", "sourceId": "1", "subSourceId": "18", "sourceKind": "RESERVATION", "externalRelationId": "1", "externalRelationKind": "ADDON", "originId": "123", "routedFrom": null, "quantity": 1, "description": "Fee 10%", "userId": "0", "sourceDatetime": "2023-07-04T14:01:17Z", "transactionDatetime": "2023-07-04T14:01:17Z", "transactionDatetimePropertyTime": "2023-07-04T14:01:17Z", "createdAt": "2023-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************-1", "subSourceIdentifier": null}, {"id": "4", "propertyId": "1", "internalTransactionCode": "7000", "amount": -10.0, "currency": "USD", "customerId": "184075", "rootId": "4", "parentId": null, "sourceId": "1", "subSourceId": "18", "sourceKind": "RESERVATION", "externalRelationId": "****************", "externalRelationKind": "ACCOUNTS_RECEIVABLE", "originId": null, "routedFrom": null, "quantity": 1, "description": "Transfer to City Ledger", "userId": "12", "sourceDatetime": "2023-11-04T14:01:17Z", "transactionDatetime": "2023-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2023-11-04T14:01:17Z", "createdAt": "2023-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": "************-1"}, {"id": "5", "propertyId": "1", "internalTransactionCode": "7000", "amount": 10.0, "currency": "USD", "customerId": "184075", "rootId": "5", "parentId": null, "sourceId": "1", "subSourceId": null, "sourceKind": "ACCOUNTS_RECEIVABLE_LEDGER", "externalRelationId": "****************", "externalRelationKind": "ACCOUNTS_RECEIVABLE", "originId": null, "routedFrom": null, "quantity": 1, "description": "Transfer to City Ledger", "userId": "12", "sourceDatetime": "2023-11-04T14:01:17Z", "transactionDatetime": "2023-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2023-11-04T14:01:17Z", "createdAt": "2023-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": null}, {"id": "6", "propertyId": "1", "sourceDatetime": "2022-11-04T14:01:17Z", "transactionDatetime": "2022-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2022-07-04T14:01:17Z", "amount": -10.0, "currency": "USD", "customerId": "184078", "parentId": null, "sourceId": "1", "sourceKind": "RESERVATION", "subSourceId": "18", "externalRelationId": "****************", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": null, "rootId": "6", "quantity": 1, "description": "Payment - Cash", "internalTransactionCode": "9100", "userId": "12", "createdAt": "2023-07-04T14:01:17Z", "account": {"id": "1", "description": "description", "name": "DEPOSIT", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES"}, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": "************-1"}, {"id": "7", "propertyId": "1", "sourceDatetime": "2022-11-04T14:01:17Z", "transactionDatetime": "2022-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2022-07-04T14:01:17Z", "amount": 3.58, "currency": "USD", "customerId": "184078", "parentId": null, "sourceId": "9", "sourceKind": "GROUP_PROFILE", "subSourceId": 18, "externalRelationId": "****************", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": null, "rootId": "6", "quantity": 1, "description": "Payment - Cash", "internalTransactionCode": "9100", "userId": "12", "createdAt": "2023-07-04T14:01:17Z", "account": {"id": "1", "description": "description", "name": "DEPOSIT", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES"}, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": null}, {"id": "8", "propertyId": "1", "sourceDatetime": "2024-11-04T14:01:17Z", "transactionDatetime": "2024-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2024-07-04T14:01:17Z", "amount": 7.31, "currency": "USD", "customerId": "184078", "parentId": null, "sourceId": "9", "sourceKind": "GROUP_PROFILE", "subSourceId": null, "externalRelationId": "****************", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": null, "rootId": "6", "quantity": 1, "description": "Payment - Cash", "internalTransactionCode": "9100", "userId": "12", "createdAt": "2023-07-04T14:01:17Z", "account": {"id": "1", "description": "description", "name": "DEPOSIT", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES"}, "customTransactionCode": "C300", "sourceIdentifier": "g222213", "subSourceIdentifier": null}, {"id": "10", "propertyId": "2", "sourceDatetime": "2024-11-04T14:01:17Z", "transactionDatetime": "2024-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2024-07-04T14:01:17Z", "amount": -5.3, "currency": "USD", "customerId": "184079", "parentId": null, "sourceId": "10", "sourceKind": "GROUP_PROFILE", "subSourceId": 4, "externalRelationId": "****************", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": null, "rootId": "6", "quantity": 1, "description": "Payment - Cash", "internalTransactionCode": "9100", "userId": "12", "createdAt": "2023-07-04T14:01:17Z", "account": {"id": "1", "description": "description", "name": "DEPOSIT", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES"}, "customTransactionCode": "C300", "sourceIdentifier": "g222214", "subSourceIdentifier": null}, {"id": "11", "propertyId": "1", "sourceDatetime": "2024-11-04T14:01:17Z", "transactionDatetime": "2024-11-04T14:01:17Z", "transactionDatetimePropertyTime": "2024-07-04T14:01:17Z", "amount": 3.5, "currency": "USD", "customerId": "184079", "parentId": null, "sourceId": "11", "sourceKind": "GROUP_PROFILE", "subSourceId": 4, "externalRelationId": "****************", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": null, "rootId": "6", "quantity": 1, "description": "Payment - Cash", "internalTransactionCode": "9100", "userId": "12", "createdAt": "2023-07-04T14:01:17Z", "account": {"id": "1", "description": "description", "name": "DEPOSIT", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES"}, "customTransactionCode": "C300", "sourceIdentifier": "g222216", "subSourceIdentifier": null}, {"id": "12", "propertyId": "1", "internalTransactionCode": "3000", "amount": 101.22, "currency": "USD", "customerId": "184074", "rootId": "12", "parentId": null, "sourceId": "2", "subSourceId": "2", "sourceKind": "RESERVATION", "externalRelationId": "1", "externalRelationKind": "ADDON", "originId": "211", "routedFrom": null, "quantity": 1, "description": "Breakfast", "userId": "0", "sourceDatetime": "2025-07-04T14:01:17Z", "transactionDatetime": "2025-07-04T14:01:17Z", "transactionDatetimePropertyTime": "2025-07-04T14:01:17Z", "createdAt": "2025-07-04T14:01:17Z", "account_id": null, "customTransactionCode": "C300", "sourceIdentifier": "************", "subSourceIdentifier": null}]