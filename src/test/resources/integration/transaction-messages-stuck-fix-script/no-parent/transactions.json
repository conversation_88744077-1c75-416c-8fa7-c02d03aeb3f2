[{"id": ****************, "propertyId": 1, "sourceDatetime": "2017-07-04T13:04:11", "transactionDatetime": "2017-07-04T13:04:11", "transactionDatetimePropertyTime": "2017-07-04T09:04:11", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": -300, "currency": "USD", "customerId": 4536485, "accountId": null, "rootId": ****************, "parentId": null, "sourceId": 4426790, "subSourceId": 6562011, "sourceKind": "RESERVATION", "externalRelationId": "5102705", "externalRelationKind": "PAYMENT", "originId": "cards_visa", "routedFrom": null, "quantity": 1, "description": "Visa", "userId": 13976, "notes": "", "serviceDate": "2017-07-04"}, {"id": ****************, "propertyId": 1, "sourceDatetime": "2017-07-04T10:51:22", "transactionDatetime": "2017-07-04T10:51:22", "transactionDatetimePropertyTime": "2017-07-04T06:51:22", "internalTransactionCode": {"id": 9, "code": "ITEM_SERVICE"}, "amount": 300, "currency": "USD", "customerId": 4536485, "accountId": null, "rootId": ****************, "parentId": null, "sourceId": 4426790, "subSourceId": 6562011, "sourceKind": "RESERVATION", "externalRelationId": "2025453", "externalRelationKind": "ITEM", "originId": "75812", "routedFrom": null, "quantity": 1, "description": "Ice Cream (<PERSON><PERSON><PERSON>)", "userId": 13976, "notes": "", "serviceDate": "2017-07-04"}]