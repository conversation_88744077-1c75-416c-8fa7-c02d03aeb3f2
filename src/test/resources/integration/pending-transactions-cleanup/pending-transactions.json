[{"id": 1, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 10022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": 999, "routedSourceKind": "GROUP_PROFILE", "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ADDON", "routedFrom": null, "originId": "211", "rootId": 1, "quantity": 1, "description": "Breakfast (package inclusive)", "internalTransactionCode": {"id": 11, "code": "ADDON"}, "userId": 0, "state": "PENDING", "status": "PENDING"}, {"id": 3, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 1002, "currency": "USD", "customerId": 184073, "parentId": 1, "sourceId": 1, "sourceKind": "RESERVATION", "subSourceId": 1, "routedSourceId": 999, "routedSourceKind": "GROUP_PROFILE", "externalRelationId": "1", "externalRelationKind": "ADDON", "routedFrom": null, "originId": "123", "rootId": 3, "quantity": 1, "description": "Tax 10%", "internalTransactionCode": {"id": 13, "code": "TAX"}, "userId": 0, "state": "POSTED", "status": "POSTED"}, {"id": 2, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 1002, "currency": "USD", "customerId": 184073, "parentId": 1, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": null, "routedSourceKind": null, "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ADDON", "routedFrom": null, "originId": "123", "rootId": 2, "quantity": 1, "description": "Fee 10%", "internalTransactionCode": {"id": 15, "code": "FEE"}, "userId": 0, "state": "PENDING", "status": "CANCELLED"}, {"id": 6, "propertyId": 1, "sourceDatetime": "2025-01-13T14:01:17", "transactionDatetime": "2025-01-13T14:01:17", "transactionDatetimePropertyTime": "2025-01-13T14:01:17", "serviceDate": "2023-01-13", "amount": 10022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": null, "routedSourceKind": null, "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ADJUSTMENT", "routedFrom": null, "originId": "211", "rootId": 6, "quantity": 1, "description": "Breakfast adjustment", "internalTransactionCode": {"id": 10, "code": "ITEM_SERVICE_A"}, "userId": 0, "state": "POSTED", "status": "POSTED"}, {"id": 7, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 10022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": null, "routedSourceKind": null, "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ROOM_REVENUE", "routedFrom": null, "originId": "211", "rootId": 7, "quantity": 1, "description": "Cancellation room revenue", "internalTransactionCode": {"id": 5, "code": "CANCELLATION"}, "userId": 0, "state": "PENDING", "status": "CANCELLED"}, {"id": 8, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 10022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": null, "routedSourceKind": null, "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "TAX", "routedFrom": null, "originId": "211", "rootId": 8, "quantity": 1, "description": "Tax", "internalTransactionCode": {"id": 13, "code": "TAX"}, "userId": 0, "state": "PENDING", "status": "CANCELLED"}, {"id": 9, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 10022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": null, "routedSourceKind": null, "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "FEE", "routedFrom": null, "originId": "211", "rootId": 9, "quantity": 1, "description": "Fee", "internalTransactionCode": {"id": 15, "code": "FEE"}, "userId": 0, "state": "PENDING", "status": "CANCELLED"}, {"id": 10, "propertyId": 1, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 12022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": 999, "routedSourceKind": "GROUP_PROFILE", "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ADDON", "routedFrom": null, "originId": "211", "rootId": 1, "quantity": 1, "description": "Breakfast (package inclusive)", "internalTransactionCode": {"id": 11, "code": "ADDON"}, "userId": 0, "state": "POSTED", "status": "POSTED"}, {"id": 11, "propertyId": 2, "sourceDatetime": "2023-07-04T14:01:17", "transactionDatetime": "2023-07-04T14:01:17", "transactionDatetimePropertyTime": "2023-07-04T14:01:17", "serviceDate": "2023-07-04", "amount": 12022, "currency": "USD", "customerId": 184074, "parentId": null, "sourceId": 1, "sourceKind": "RESERVATION", "routedSourceId": 999, "routedSourceKind": "GROUP_PROFILE", "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "ADDON", "routedFrom": null, "originId": "211", "rootId": 11, "quantity": 1, "description": "Breakfast (package inclusive)", "internalTransactionCode": {"id": 11, "code": "ADDON"}, "userId": 0, "state": "POSTED", "status": "POSTED"}]