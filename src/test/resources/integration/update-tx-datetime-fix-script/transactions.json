[{"id": 1, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": -1, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "1", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "cards_visa", "rootId": 1, "quantity": 0, "description": "Credit Card - Visa x4242 - Payment Processed by Payment Gateway", "userId": 5, "notes": "some note", "account": null}, {"id": 2, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": -2, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "2", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "cards_visa", "rootId": 2, "quantity": 0, "description": "Credit Card - Visa x4242 - Payment Processed by Payment Gateway", "userId": 5, "notes": "some note", "account": null}, {"id": 3, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": -3, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "3", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "cards_visa", "rootId": 3, "quantity": 0, "description": "Credit Card - Visa x4242 - Payment Processed by Payment Gateway", "userId": 5, "notes": "some note", "account": null}]