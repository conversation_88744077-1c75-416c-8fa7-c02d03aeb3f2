[{"id": 1, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -10022, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "11", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 1, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0}, {"id": 2, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -2222, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "22", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 1, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 31, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -3111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "33", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 31, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 32, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -3222, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "33", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 32, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0}, {"id": 41, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -4111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "44", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 41, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 42, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": 4111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "44", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 41, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 56, "code": "PAYMENT_BANK_V"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 51, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -5111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "55", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 51, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 52, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": 5111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "55", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 51, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 39, "code": "DEPOSIT_TRANSFER"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 53, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -5111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130201, "sourceKind": "GROUP_PROFILE", "subSourceId": 1, "externalRelationId": "55", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 51, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 61, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -6111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "66", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 61, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}, {"id": 62, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": 6111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "66", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 61, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 41, "code": "PAYMENT_BANK_R"}, "userId": 0, "account": {"name": "DEPOSIT", "description": "description", "category": "DEPOSITS", "chartOfAccountType": "LIABILITIES", "id": 1, "propertyId": 1, "createdBy": 1}}]