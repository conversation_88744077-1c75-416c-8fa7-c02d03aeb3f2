[{"id": 1, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "amount": -10022, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "11", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "ebanking", "rootId": 1, "quantity": 0, "description": "visax4242 - gateway", "notes": "", "userId": 5}, {"id": 3, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "internalTransactionCode": {"id": 23, "code": "REFUND_BANK"}, "amount": 10022, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "11", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "ebanking", "rootId": 1, "quantity": 0, "description": "visax4242 - gateway", "notes": "", "userId": 5}]