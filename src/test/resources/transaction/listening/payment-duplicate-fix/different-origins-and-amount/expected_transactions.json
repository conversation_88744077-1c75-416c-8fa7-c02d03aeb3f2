[{"id": **************, "propertyId": 1, "sourceDatetime": "2024-10-30T04:18:03", "transactionDatetime": "2024-10-30T04:18:03", "transactionDatetimePropertyTime": "2024-10-30T00:18:03", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": 50000, "currency": "USD", "customerId": *********, "accountId": null, "rootId": **************, "parentId": null, "sourceId": *********, "subSourceId": *********, "sourceKind": "RESERVATION", "externalRelationId": "*********", "externalRelationKind": "PAYMENT", "originId": "cards_Discover", "routedFrom": null, "quantity": 1, "description": "Discover", "userId": 0, "createdAt": "2024-10-30T04:18:03", "version": 0, "notes": "", "serviceDate": "2024-10-30"}, {"id": **************, "propertyId": 1, "sourceDatetime": "2024-10-30T04:18:03", "transactionDatetime": "2024-10-30T04:18:12", "transactionDatetimePropertyTime": "2024-10-30T00:18:12", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": 65000, "currency": "USD", "customerId": null, "accountId": null, "rootId": **************, "parentId": null, "sourceId": *********, "subSourceId": *********, "sourceKind": "RESERVATION", "externalRelationId": "*********", "externalRelationKind": "PAYMENT", "originId": "cards_discover", "routedFrom": null, "quantity": 1, "description": "Discoverx4040 - StripePlatformGateway", "userId": 520235, "createdAt": "2024-10-30T04:18:12", "version": 0, "notes": "", "serviceDate": "2024-10-30"}]