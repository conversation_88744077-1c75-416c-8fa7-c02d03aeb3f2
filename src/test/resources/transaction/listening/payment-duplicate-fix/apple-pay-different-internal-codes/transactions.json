[{"id": **************, "propertyId": 1, "sourceDatetime": "2025-01-15T13:21:54", "transactionDatetime": "2025-01-15T13:21:59", "transactionDatetimePropertyTime": "2025-01-15T04:21:59", "internalTransactionCode": {"id": 17, "code": "PAYMENT"}, "amount": 30582, "currency": "USD", "customerId": null, "accountId": null, "rootId": **************, "parentId": null, "sourceId": *********, "subSourceId": null, "sourceKind": "RESERVATION", "externalRelationId": "*********", "externalRelationKind": "PAYMENT", "originId": "apple_pay_visa", "routedFrom": null, "quantity": 1, "description": "Apple Pay - Visa x8664 - Payment Processed by Cloudbeds Payments", "userId": 0, "createdAt": "2025-01-15T13:21:59", "version": 0, "notes": "Payment from Pay By Link", "serviceDate": "2025-01-15"}, {"id": **************, "propertyId": 1, "sourceDatetime": "2025-01-15T13:21:54", "transactionDatetime": "2025-01-15T13:21:55", "transactionDatetimePropertyTime": "2025-01-15T04:21:55", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": 30582, "currency": "USD", "customerId": *********, "accountId": null, "rootId": **************, "parentId": null, "sourceId": *********, "subSourceId": *********, "sourceKind": "RESERVATION", "externalRelationId": "*********", "externalRelationKind": "PAYMENT", "originId": "cards", "routedFrom": null, "quantity": 1, "description": "Credit Card", "userId": 0, "createdAt": "2025-01-15T13:21:55", "version": 0, "notes": "", "serviceDate": "2025-01-15"}]