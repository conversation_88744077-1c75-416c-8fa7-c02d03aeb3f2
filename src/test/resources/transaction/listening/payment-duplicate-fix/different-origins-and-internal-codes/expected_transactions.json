[{"id": **************, "propertyId": 1, "sourceDatetime": "2024-10-09T03:02:10", "transactionDatetime": "2024-10-09T03:02:10", "transactionDatetimePropertyTime": "2024-10-08T22:02:10", "internalTransactionCode": {"id": 20, "code": "PAYMENT_CARD"}, "amount": 15000, "currency": "USD", "customerId": *********, "accountId": null, "rootId": **************, "parentId": null, "sourceId": *********, "subSourceId": *********, "sourceKind": "RESERVATION", "externalRelationId": "*********", "externalRelationKind": "PAYMENT", "originId": "cards_discover", "routedFrom": null, "quantity": 1, "description": "Discoverx6452 - StripePlatformGateway", "userId": 0, "createdAt": "2024-10-09T03:02:30", "version": 1, "notes": "", "serviceDate": "2024-10-08"}]