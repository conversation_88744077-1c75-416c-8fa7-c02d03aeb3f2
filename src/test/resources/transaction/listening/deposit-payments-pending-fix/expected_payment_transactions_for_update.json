[{"id": 2, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -2222, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "22", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 1, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "state": "PENDING", "status": "PENDING", "updatedAt": "2023-07-04T13:47:51.000Z"}, {"id": 41, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -4111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "44", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 41, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "state": "PENDING", "status": "PENDING", "updatedAt": "2023-07-04T13:47:51.000Z"}, {"id": 42, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": 4111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "44", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 41, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 56, "code": "PAYMENT_BANK_V"}, "userId": 0, "state": "PENDING", "status": "PENDING", "updatedAt": "2023-07-04T13:47:51.000Z"}, {"id": 61, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": -6111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "66", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 61, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 19, "code": "PAYMENT_BANK"}, "userId": 0, "state": "PENDING", "status": "PENDING", "updatedAt": "2023-07-04T13:47:51.000Z"}, {"id": 62, "propertyId": 1, "sourceDatetime": "2023-07-04T13:47:51", "transactionDatetime": "2023-07-04T13:47:51", "transactionDatetimePropertyTime": "2023-07-04T13:47:51", "serviceDate": "2023-07-04", "amount": 6111, "currency": "USD", "customerId": 184073, "parentId": null, "sourceId": 130200, "sourceKind": "RESERVATION", "subSourceId": 1, "externalRelationId": "66", "externalRelationKind": "PAYMENT", "routedFrom": null, "originId": "card", "rootId": 61, "quantity": 1, "description": "", "notes": "", "internalTransactionCode": {"id": 41, "code": "PAYMENT_BANK_R"}, "userId": 0, "state": "PENDING", "status": "PENDING", "updatedAt": "2023-07-04T13:47:51.000Z"}]